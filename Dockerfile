# Базовый образ
FROM node:18-alpine AS base

# Этап 1: Uptime Kuma
FROM louislam/uptime-kuma:1 AS uptime-kuma

# Этап 2: Xray-checker
FROM kutovoys/xray-checker AS xray-checker

# Финальный образ
FROM base AS runtime

# Установка необходимых пакетов
RUN apk add --no-cache curl dumb-init

# Копирование файлов из Uptime Kuma
COPY --from=uptime-kuma /app /app
WORKDIR /app

# Копирование файлов из Xray-checker
COPY --from=xray-checker /app /xray-checker

# Создание необходимых директорий
RUN mkdir -p /app/data

# Переменная окружения для xray-checker
ENV SUBSCRIPTION_URL=""

# Экспозим порты для обоих сервисов
EXPOSE 3001 2112

# Настройка healthcheck
HEALTHCHECK --interval=30s --timeout=10s --retries=3 --start-period=40s \
  CMD curl -f http://localhost:2112/health || exit 1

# Создаем скрипт запуска для обоих сервисов
RUN echo '#!/bin/sh\n\
echo "Starting xray-checker..."\n\
cd /xray-checker && ./xray-checker &\n\
echo "Starting uptime-kuma..."\n\
cd /app && node server/server.js\n' > /start.sh && chmod +x /start.sh

# Запуск сервисов
CMD ["/usr/bin/dumb-init", "--", "/start.sh"]
