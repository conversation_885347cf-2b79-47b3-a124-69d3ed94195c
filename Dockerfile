# Базовый образ
FROM node:18-alpine AS base

# Этап 1: Uptime Kuma
FROM louislam/uptime-kuma:1 AS uptime-kuma

# Этап 2: Xray-checker
FROM kutovoys/xray-checker AS xray-checker

# Финальный образ
FROM base AS runtime

# Установка необходимых пакетов
RUN apk add --no-cache curl dumb-init

# Копирование файлов из Uptime Kuma
COPY --from=uptime-kuma /app /app
WORKDIR /app

# Копирование исполняемого файла xray-checker из корня образа
COPY --from=xray-checker /xray-checker /usr/local/bin/xray-checker
RUN chmod +x /usr/local/bin/xray-checker

# Создание необходимых директорий
RUN mkdir -p /app/data

# Переменная окружения для xray-checker
ENV SUBSCRIPTION_URL=""

# Экспозим порт uptime kuma
EXPOSE 3001

# Настройка healthcheck
HEALTHCHECK --interval=30s --timeout=10s --retries=3 --start-period=40s \
  CMD curl -f http://localhost:2112/health || exit 1

# Копируем скрипт запуска
COPY start.sh /start.sh
RUN chmod +x /start.sh

# Запуск сервисов
ENTRYPOINT ["/usr/bin/dumb-init", "--"]
CMD ["/start.sh"]
