#!/bin/sh
set -e

echo "Starting xray-checker..."
/usr/local/bin/xray-checker &

# Ждем пока xray-checker станет доступен и выводим его страницу один раз
echo "Waiting for xray-checker to be ready..."
while ! curl -s http://localhost:2112 >/dev/null 2>&1; do
    sleep 2
done

echo "=== XRAY-CHECKER STATUS PAGE ==="
curl -s http://localhost:2112
echo ""
echo "=== END XRAY-CHECKER STATUS ==="

echo "Starting uptime-kuma..."
echo "==> Performing startup jobs and maintenance tasks"
chown -hRc 0:0 /app/data
echo "==> Starting application with user 0 group 0"
cd /app
exec node server/server.js
