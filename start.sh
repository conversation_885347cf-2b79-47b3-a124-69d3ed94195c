#!/bin/sh
set -e

echo "Starting xray-checker..."
/usr/local/bin/xray-checker &
XRAY_PID=$!

# Функция для вывода HTML страницы xray-checker в логи
log_xray_status() {
    while true; do
        sleep 30  # Ждем 30 секунд перед первой проверкой
        if curl -s http://localhost:2112 >/dev/null 2>&1; then
            echo "=== XRAY-CHECKER STATUS PAGE ==="
            curl -s http://localhost:2112 | head -50
            echo "=== END XRAY-CHECKER STATUS ==="
        else
            echo "Xray-checker not ready yet, waiting..."
        fi
        sleep 300  # Повторяем каждые 5 минут
    done
}

# Запускаем функцию логирования в фоне
log_xray_status &

echo "Starting uptime-kuma..."
echo "==> Performing startup jobs and maintenance tasks"
chown -hRc 0:0 /app/data
echo "==> Starting application with user 0 group 0"
cd /app
exec node server/server.js
