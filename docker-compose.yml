services:
  monitoring:
    build:
      context: .
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - SUBSCRIPTION_URL=${SUBSCRIPTION_URL}
    ports:
      - "3001:3001"
    volumes:
      - uptime-kuma:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:2112/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  uptime-kuma:
