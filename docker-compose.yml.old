services:
  xray-checker:
    image: kutovoys/xray-checker
    environment:
      - SUBSCRIPTION_URL=https://8.8880500.xyz/5trr62xgR1dNw3HX
    ports:
      - "127.0.0.1:2112:2112"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:2112/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - monitoring-network

  uptime-kuma:
    image: louislam/uptime-kuma:1
    container_name: uptime-kuma
    restart: unless-stopped
    ports:
      - "3001:3001"
    volumes:
      - uptime-kuma:/app/data
    networks:
      - monitoring-network

volumes:
  uptime-kuma:

networks:
  monitoring-network:
    driver: bridge
    name: monitoring-network
