<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/svg+xml" href="/icon.svg" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" id="theme-color" content="" />
    <meta name="description" content="Uptime Kuma monitoring tool" />
    <title>Uptime Kuma</title>
    <style>        .noscript-message {
            font-size: 20px;
            text-align: center;
            padding: 10px;
            max-width: 500px;
            margin: 0 auto;
        }
    </style>
  <script type="module" crossorigin src="/assets/index-B_z9mVlf.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-bOVKKa1O.css">
  <script type="module">import.meta.url;import("_").catch(()=>1);async function* g(){};if(location.protocol!="file:"){window.__vite_is_modern_browser=true}</script>
  <script type="module">!function(){if(window.__vite_is_modern_browser)return;console.warn("vite: loading legacy chunks, syntax error above and the same error below should be ignored");var e=document.getElementById("vite-legacy-polyfill"),n=document.createElement("script");n.src=e.src,n.onload=function(){System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))},document.body.appendChild(n)}();</script>
</head>
<body>
<noscript>
<div class="noscript-message">
    Sorry, you don't seem to have JavaScript enabled or your browser
    doesn't support it.<br />This website requires JavaScript to function.
    Please enable JavaScript in your browser settings to continue.
</div>
</noscript>
<div id="app"></div>
  <script nomodule>!function(){var e=document,t=e.createElement("script");if(!("noModule"in t)&&"onbeforeload"in t){var n=!1;e.addEventListener("beforeload",(function(e){if(e.target===t)n=!0;else if(!e.target.hasAttribute("nomodule")||!n)return;e.preventDefault()}),!0),t.type="module",t.src=".",e.head.appendChild(t),t.remove()}}();</script>
  <script nomodule crossorigin id="vite-legacy-polyfill" src="/assets/polyfills-legacy-CProAAlf.js"></script>
  <script nomodule crossorigin id="vite-legacy-entry" data-src="/assets/index-legacy-PIuZWdNf.js">System.import(document.getElementById('vite-legacy-entry').getAttribute('data-src'))</script>
</body>
</html>
